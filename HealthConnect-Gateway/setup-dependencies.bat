@echo off
echo Installing HealthConnect Gateway Dependencies...

echo.
echo 1. Installing Docker Desktop (if not installed)
echo Please download and install Docker Desktop from:
echo https://www.docker.com/products/docker-desktop/
echo.
pause

echo.
echo 2. Starting Redis container...
docker run -d --name healthconnect-redis -p 6379:6379 redis:alpine
echo Redis started on port 6379

echo.
echo 3. Starting MongoDB container...
docker run -d --name healthconnect-mongodb -p 27017:27017 mongo:latest
echo MongoDB started on port 27017

echo.
echo 4. Starting Keycloak (OAuth2 Server) container...
docker run -d --name healthconnect-keycloak -p 8080:8080 -e KEYCLOAK_ADMIN=admin -e KEYCLOAK_ADMIN_PASSWORD=admin quay.io/keycloak/keycloak:latest start-dev
echo Keycloak started on port 8080

echo.
echo 5. Verifying installations...
timeout /t 10 /nobreak > nul

echo Testing Redis...
docker exec healthconnect-redis redis-cli ping

echo Testing MongoDB...
docker exec healthconnect-mongodb mongosh --eval "db.runCommand('ping')"

echo.
echo Setup complete! Services running:
echo - Redis: localhost:6379
echo - MongoDB: localhost:27017  
echo - Keycloak: http://localhost:8080 (admin/admin)
echo.
echo To stop all services: docker stop healthconnect-redis healthconnect-mongodb healthconnect-keycloak
echo To start all services: docker start healthconnect-redis healthconnect-mongodb healthconnect-keycloak
pause
