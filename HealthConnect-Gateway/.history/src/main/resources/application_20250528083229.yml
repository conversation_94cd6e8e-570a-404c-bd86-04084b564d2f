server:
  port: 8911
spring:
  application:
    name: HealthConnectGateway

  cloud:
    gateway:
      routes:
        - id: Awash_service
          uri: http://localhost:8888
          predicates:
            - Path=/Awash/api/**
          filters:
            - AddRequestHeader=X-Provider-Request, true
            - RewritePath=/Awash/(?<segment>.*), /${segment}
        - id: Nyala_service
          uri: http://nyalabackend.medcoanalytics.com
          predicates:
            - Path=/Nyala/api/**
          filters:
            - RewritePath=/Nyala/(?<segment>.*), /${segment}

logging:
  level:
    org.springframework.cloud.gateway: DEBUG
    org.springframework.web: DEBUG
#    security:
#        csrf:
#            enabled=false:
#    main:
#        web-application-type: reactive
#    cloud:
#        gateway:
#            globalcors:
#                cors-configurations:
#                    '[/**]':
#                        allowedOrigins: "*"
#                        allowedHeaders: "*"
#                        allowedMethods:
#                            - GET
#                            - POST
#                            - PUT
#                            - DELETE
#            discovery:
#                locator:
#                    enabled: true
#            routes:
#                - id:
#                  uri: ${CLAIMCONNECT_URI:lb://claimconnect-payer-service}
#                  predicates:
#                      - Path=/api/payer/claimconnect/**
#                - id: lifeservice
#                  uri: ${LIFE_URI:lb://lifeservice}
#                  predicates:
#                      - Path=/api/life/**
#                  filters:
#                      - AuthenticationFilter
#                - id: authservice
#                  uri: ${AUTH_URI:lb://authservice}
#                  predicates:
#                      - Path=/api/eix/auth/users/**

