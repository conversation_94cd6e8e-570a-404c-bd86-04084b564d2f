server:
  port: 8911
spring:
  application:
    name: HealthConnectGateway

  cloud:
    gateway:
      routes:
        - id: Awash_service
          uri: http://localhost:8888
          predicates:
            - Path=/Awash/api/**
          filters:
            - AddRequestHeader=X-Provider-Request, true
            - RewritePath=/Awash/(?<segment>.*), /${segment}
        - id: Nyala_service
          uri: http://nyalabackend.medcoanalytics.com
          predicates:
            - Path=/Nyala/api/**
          filters:
            - RewritePath=/Nyala/(?<segment>.*), /${segment}
