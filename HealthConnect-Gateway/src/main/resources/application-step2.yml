server:
  port: 8911

spring:
  application:
    name: HealthConnectGateway
  main:
    allow-bean-definition-overriding: true
  cloud:
    gateway:
      routes:
        - id: test_route
          uri: http://httpbin.org
          predicates:
            - Path=/test/**
          filters:
            - RewritePath=/test/(?<segment>.*), /${segment}
        - id: health_route
          uri: http://localhost:8911
          predicates:
            - Path=/actuator/**

management:
  endpoints:
    web:
      exposure:
        include: health, info, prometheus, metrics
  endpoint:
    health:
      show-details: always
    prometheus:
      enabled: true
  observations:
    http:
      client:
        requests:
          name: http.client.requests

logging:
  level:
    org.springframework.cloud.gateway: INFO
    com.medco.HealthConnect: INFO
