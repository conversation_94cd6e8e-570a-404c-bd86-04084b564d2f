# Development profile configuration
spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          # Use mock values for development
          issuer-uri: http://localhost:8080/realms/healthconnect
          jwk-set-uri: http://localhost:8080/realms/healthconnect/protocol/openid-connect/certs

# Disable security for development (optional)
management:
  security:
    enabled: false

# Enable debug logging for development
logging:
  level:
    org.springframework.cloud.gateway: DEBUG
    org.springframework.security: DEBUG
    com.medco.HealthConnect: DEBUG
    org.springframework.web: DEBUG
    reactor.netty: DEBUG

# Development-specific settings
healthconnect:
  audit:
    enabled: false  # Disable audit logging in dev
  rate-limiting:
    enabled: false  # Disable rate limiting in dev
