server:
  port: 8911

spring:
  application:
    name: HealthConnectGateway
  main:
    allow-bean-definition-overriding: true
  cloud:
    gateway:
      routes:
        # Test route
        - id: test_route
          uri: http://httpbin.org
          predicates:
            - Path=/test/**
          filters:
            - RewritePath=/test/(?<segment>.*), /${segment}

        # Health and monitoring
        - id: health_route
          uri: http://localhost:8911
          predicates:
            - Path=/actuator/**

management:
  endpoints:
    web:
      exposure:
        include: health, info, prometheus, metrics
  endpoint:
    health:
      show-details: always
    prometheus:
      enabled: true

# SpringDoc OpenAPI Configuration
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    try-it-out-enabled: true

logging:
  level:
    org.springframework.cloud.gateway: INFO
    com.medco.HealthConnect: INFO
