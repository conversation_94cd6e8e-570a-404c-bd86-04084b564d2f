server:
  port: 8911

spring:
  application:
    name: HealthConnectGateway
  main:
    allow-bean-definition-overriding: true
  cloud:
    gateway:
      routes:
        - id: test_route
          uri: http://httpbin.org
          predicates:
            - Path=/test/**
          filters:
            - RewritePath=/test/(?<segment>.*), /${segment}

management:
  endpoints:
    web:
      exposure:
        include: health, info
  endpoint:
    health:
      show-details: always

logging:
  level:
    org.springframework.cloud.gateway: DEBUG
    com.medco.HealthConnect: DEBUG
