server:
  port: 8911

spring:
  application:
    name: HealthConnectGateway
  main:
    allow-bean-definition-overriding: true
  cloud:
    gateway:
      routes:
        - id: test_route
          uri: http://httpbin.org
          predicates:
            - Path=/test/**
          filters:
            - RewritePath=/test/(?<segment>.*), /${segment}
            - name: CircuitBreaker
              args:
                name: testCircuitBreaker
                fallbackUri: forward:/fallback/test
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
                key-resolver: "#{@userKeyResolver}"
        - id: health_route
          uri: http://localhost:8911
          predicates:
            - Path=/actuator/**
  redis:
    host: localhost
    port: 6379
    timeout: 2000ms
  data:
    mongodb:
      host: localhost
      port: 27017
      database: healthconnect_audit
      auto-index-creation: true

management:
  endpoints:
    web:
      exposure:
        include: health, info, prometheus, metrics, circuitbreakers
  endpoint:
    health:
      show-details: always
    prometheus:
      enabled: true
  observations:
    http:
      client:
        requests:
          name: http.client.requests

# Circuit Breaker Configuration
resilience4j:
  circuitbreaker:
    instances:
      testCircuitBreaker:
        registerHealthIndicator: true
        slidingWindowSize: 10
        minimumNumberOfCalls: 5
        permittedNumberOfCallsInHalfOpenState: 3
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 5s
        failureRateThreshold: 50
        eventConsumerBufferSize: 10

# Audit Configuration
healthconnect:
  audit:
    enabled: true
    retention-days: 30

# SpringDoc OpenAPI Configuration
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    try-it-out-enabled: true

logging:
  level:
    org.springframework.cloud.gateway: INFO
    com.medco.HealthConnect: INFO
    io.github.resilience4j: DEBUG
