server:
  port: 8911

spring:
  application:
    name: HealthConnectGateway
  main:
    allow-bean-definition-overriding: true
  cloud:
    gateway:
      routes:
        # Test route to httpbin (external service)
        - id: test_route
          uri: http://httpbin.org
          predicates:
            - Path=/test/**
          filters:
            - RewritePath=/test/(?<segment>.*), /${segment}
        
        # Awash Service Route (will fail but shows routing)
        - id: awash_service
          uri: http://localhost:8888
          predicates:
            - Path=/Awash/api/**
          filters:
            - RewritePath=/Awash/(?<segment>.*), /${segment}
            - AddRequestHeader=X-Provider-Request, true
        
        # Nyala Service Route (will fail but shows routing)
        - id: nyala_service
          uri: http://nyalabackend.medcoanalytics.com
          predicates:
            - Path=/Nyala/api/**
          filters:
            - RewritePath=/Nyala/(?<segment>.*), /${segment}

management:
  endpoints:
    web:
      exposure:
        include: health, info, metrics
  endpoint:
    health:
      show-details: always

# SpringDoc OpenAPI Configuration
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    try-it-out-enabled: true

logging:
  level:
    org.springframework.cloud.gateway: INFO
    com.medco.HealthConnect: INFO
