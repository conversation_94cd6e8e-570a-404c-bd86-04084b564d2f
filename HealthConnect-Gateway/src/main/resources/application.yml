server:
  port: 8911
spring:
  application:
    name: HealthConnectGateway
  main:
    allow-bean-definition-overriding: true
  cloud:
    gateway:
      routes:
        - id: Awash_service
          uri: http://192.168.100.85:8888
          predicates:
            - Path=/Awash/api/**
          filters:
            - name: ClaimValidation
            - name: RateLimiter
              args:
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
            - AddRequestHeader=X-Provider-Request, true
            - RewritePath=/Awash/(?<segment>.*), /${segment}
            - name: CircuitBreaker
              args:
                name: awashCircuitBreaker
                fallbackUri: forward:/fallback/awash
        - id: Nyala_service
          uri: http://nyalabackend.medcoanalytics.com
          predicates:
            - Path=/Nyala/api/**
          filters:
            - name: ClaimValidation
            - RewritePath=/Nyala/(?<segment>.*), /${segment}
            - name: CircuitBreaker
              args:
                name: nyalaCircuitBreaker
                fallbackUri: forward:/fallback/nyala
        - id: Monitoring_service
          uri: http://localhost:8911
          predicates:
            - Path=/actuator/healthconnect/**
        - id: Documentation_service
          uri: http://localhost:8911
          predicates:
            - Path=/v3/api-docs/healthconnect/**
  # Commented out for development - uncomment when OAuth2 server is available
  # security:
  #   oauth2:
  #     resourceserver:
  #       jwt:
  #         issuer-uri: ${OAUTH2_ISSUER_URI:http://localhost:8080/realms/healthconnect}
  #         jwk-set-uri: ${OAUTH2_JWKS_URI:http://localhost:8080/realms/healthconnect/protocol/openid-connect/certs}
  redis:
    host: localhost
    port: 6379
  sleuth:
    sampler:
      probability: 1.0
  zipkin:
    base-url: http://localhost:9411/

management:
  endpoints:
    web:
      exposure:
        include: health, prometheus, metrics, circuitbreakers
  endpoint:
    health:
      show-details: always
    prometheus:
      enabled: true

  observations:
    http:
      client:
        requests:
          name: http.client.requests
          low-cardinality-keys: [ method, uri, status ]
          high-cardinality-keys: [ http.url ]

logging:
  level:
    root: INFO
    org.springframework.cloud.gateway: DEBUG
    com.healthconnect.gateway: TRACE
  file:
    name: logs/healthconnect-gateway.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

resilience4j:
  circuitbreaker:
    instances:
      awashCircuitBreaker:
        registerHealthIndicator: true
        slidingWindowSize: 10
        minimumNumberOfCalls: 5
        permittedNumberOfCallsInHalfOpenState: 3
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 5s
        failureRateThreshold: 50
        eventConsumerBufferSize: 10
      nyalaCircuitBreaker:
        registerHealthIndicator: true
        slidingWindowSize: 10
        minimumNumberOfCalls: 5
        permittedNumberOfCallsInHalfOpenState: 3
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 5s
        failureRateThreshold: 50
        eventConsumerBufferSize: 10

healthconnect:
  audit:
    enabled: true
    retention-days: 30
  caching:
    enabled: true
    ttl-minutes: 15
  rate-limiting:
    enabled: true
    requests-per-minute: 100


  data:
    mongodb:
      host: ${MONGO_HOST:localhost}
      port: ${MONGO_PORT:27017}
      database: healthconnect_audit
      auto-index-creation: true

  cloud:
    gateway:
      discovery:
        locator:
          enabled: false
      metrics:
        enabled: true
      httpclient:
        pool:
          max-connections: 1000
          max-idle-time: 60s

# SpringDoc OpenAPI Configuration
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    config-url: /v3/api-docs/swagger-config
    urls:
      - url: /v3/api-docs/healthconnect-public
        name: All APIs
      - url: /v3/api-docs/awash-service
        name: Awash Service
      - url: /v3/api-docs/nyala-service
        name: Nyala Service
      - url: /v3/api-docs/monitoring
        name: Monitoring
      - url: /v3/api-docs/fallback
        name: Fallbacks
    display-request-duration: true
    show-extensions: true
    show-common-extensions: true
    default-models-expand-depth: 1
    default-model-expand-depth: 1
    try-it-out-enabled: true
  show-actuator: true
  group-configs:
    - group: healthconnect-public
      display-name: HealthConnect Gateway - All APIs
    - group: awash-service
      display-name: Awash Payer Service
    - group: nyala-service
      display-name: Nyala Payer Service
    - group: monitoring
      display-name: Monitoring & Health
    - group: fallback
      display-name: Circuit Breaker Fallbacks
