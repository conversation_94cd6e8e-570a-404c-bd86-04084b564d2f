package com.medco.HealthConnect.Config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SimpleOpenApiConfig {

    @Bean
    public OpenAPI healthConnectOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("HealthConnect Gateway API")
                        .description("API Gateway for connecting healthcare providers and payers")
                        .version("v1.0")
                        .contact(new Contact()
                                .name("HealthConnect Support")
                                .email("<EMAIL>"))
                        .license(new License()
                                .name("Apache 2.0")
                                .url("https://www.apache.org/licenses/LICENSE-2.0")))
                .addSecurityItem(new SecurityRequirement().addList("bearerAuth"))
                .components(new Components()
                        .addSecuritySchemes("bearerAuth", new SecurityScheme()
                                .name("bearerAuth")
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("bearer")
                                .bearerFormat("JWT")));
    }

    @Bean
    public GroupedOpenApi publicApi() {
        return GroupedOpenApi.builder()
                .group("healthconnect-public")
                .pathsToMatch("/**")
                .pathsToExclude("/actuator/**")
                .build();
    }

    @Bean
    public GroupedOpenApi awashApi() {
        return GroupedOpenApi.builder()
                .group("awash-service")
                .pathsToMatch("/Awash/**")
                .build();
    }

    @Bean
    public GroupedOpenApi nyalaApi() {
        return GroupedOpenApi.builder()
                .group("nyala-service")
                .pathsToMatch("/Nyala/**")
                .build();
    }
}
