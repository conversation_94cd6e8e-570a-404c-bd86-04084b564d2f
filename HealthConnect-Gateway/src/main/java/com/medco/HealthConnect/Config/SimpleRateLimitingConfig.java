package com.medco.HealthConnect.Config;

import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.core.publisher.Mono;

@Configuration
public class SimpleRateLimitingConfig {

    @Bean
    public KeyResolver userKeyResolver() {
        return exchange -> {
            // For development, use IP address as key
            String remoteAddress = exchange.getRequest().getRemoteAddress() != null 
                ? exchange.getRequest().getRemoteAddress().getAddress().getHostAddress()
                : "unknown";
            return Mono.just(remoteAddress);
        };
    }
}
