package com.medco.HealthConnect.Config;

import com.medco.HealthConnect.service.CustomRedisRateLimiter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import io.micrometer.tracing.Tracer;

@Configuration
public class GatewayConfig {

    @Bean
    public CustomRedisRateLimiter customRedisRateLimiter(Tracer tracer) {

        CustomRedisRateLimiter limiter = new CustomRedisRateLimiter(tracer);

        // Configure default rates for all routes
        CustomRedisRateLimiter.Config defaultConfig = new CustomRedisRateLimiter.Config();
        defaultConfig.setReplenishRate(10);  // 10 requests per second
        defaultConfig.setBurstCapacity(20);  // Allow bursts of 20 requests

        // Apply to all routes (or specify route-specific configs)
        limiter.configureRoute("default", defaultConfig);

        // Route-specific configuration example
        CustomRedisRateLimiter.Config awashConfig = new CustomRedisRateLimiter.Config();
        awashConfig.setReplenishRate(15);
        awashConfig.setBurstCapacity(30);
        limiter.configureRoute("awash_service", awashConfig);

        return limiter;
    }
}
