package com.medco.HealthConnect.Config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class StandaloneOpenApiConfig {

    @Bean
    public OpenAPI healthConnectOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("HealthConnect Gateway API")
                        .description("API Gateway for connecting healthcare providers and payers - Standalone Demo")
                        .version("v1.0")
                        .contact(new Contact()
                                .name("HealthConnect Support")
                                .email("<EMAIL>"))
                        .license(new License()
                                .name("Apache 2.0")
                                .url("https://www.apache.org/licenses/LICENSE-2.0")));
    }
}
