package com.medco.HealthConnect.controller;

import com.medco.HealthConnect.service.AuditService;
import io.micrometer.tracing.annotation.NewSpan;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/fallback")
public class FallbackController {

    private final AuditService auditService;

    public FallbackController(AuditService auditService) {
        this.auditService = auditService;
    }

    @GetMapping("/awash")
    @NewSpan("fallback-awash")
    public Mono<ResponseEntity<Map<String, Object>>> awashFallback(ServerWebExchange exchange) {
        return handleFallback(exchange, "Awash");
    }

    @GetMapping("/nyala")
    @NewSpan("fallback-nyala")
    public Mono<ResponseEntity<Map<String, Object>>> nyalaFallback(ServerWebExchange exchange) {
        return handleFallback(exchange, "Nyala");
    }

    private Mono<ResponseEntity<Map<String, Object>>> handleFallback(ServerWebExchange exchange, String payer) {
        return auditService.logFallback(exchange, payer + " service unavailable")
                .then(Mono.fromCallable(() -> {
                    Map<String, Object> response = new HashMap<>();
                    response.put("timestamp", Instant.now().toString());
                    response.put("status", HttpStatus.SERVICE_UNAVAILABLE.value());
                    response.put("error", "Service Unavailable");
                    response.put("message", payer + " payer service is currently unavailable. Please try again later.");
                    response.put("path", exchange.getRequest().getPath().toString());
                    response.put("traceId", exchange.getAttribute("traceId")); // Added trace ID for correlation

                    return ResponseEntity
                            .status(HttpStatus.SERVICE_UNAVAILABLE)
                            .contentType(MediaType.APPLICATION_JSON)
                            .body(response);
                }));
    }
}