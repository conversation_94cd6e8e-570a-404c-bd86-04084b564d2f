package com.medco.HealthConnect.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.Instant;
import java.util.Map;

@Document(collection = "audit_logs")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AuditEntry {
    @Id
    private String id;
    private Instant timestamp;
    private String requestId;
    private String httpMethod;
    private String path;
    private String remoteAddress;
    private String userId;
    private String userRoles;
    private Map<String, String> headers;
    private Map<String, String> queryParams;
    private String status;
    private String errorDetails;
    private String traceId;
    private String spanId;
    private String serviceName = "HealthConnectGateway";
    private Long processingTimeMs;
    private String requestBodyHash; // SHA-256 hash for sensitive data
    private String clientType; // web/mobile/api
    private String correlationId;
}