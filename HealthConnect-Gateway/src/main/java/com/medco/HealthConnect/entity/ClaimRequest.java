package com.medco.HealthConnect.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClaimRequest {

    private String patientId;
    private String providerId;
    private LocalDate serviceDate;
    private List<Procedure> procedures;
    private String diagnosisCode;
    private BigDecimal totalAmount;
    private Map<String, String> additionalInfo;

    private String traceId;
    private String spanId;

}
