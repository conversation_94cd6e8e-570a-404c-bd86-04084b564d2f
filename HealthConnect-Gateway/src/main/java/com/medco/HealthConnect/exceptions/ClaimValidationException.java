package com.medco.HealthConnect.exceptions;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.server.ServerWebExchange;

import java.util.List;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class ClaimValidationException extends RuntimeException {
    private final List<String> validationErrors;
    private final transient ServerWebExchange exchange;

    public ClaimValidationException(String message, List<String> validationErrors, ServerWebExchange exchange) {
        super(message);
        this.validationErrors = validationErrors;
        this.exchange = exchange;
    }

    public List<String> getValidationErrors() {
        return validationErrors;
    }

    public ServerWebExchange getExchange() {
        return exchange;
    }

    public String getRequestPath() {
        return exchange.getRequest().getPath().toString();
    }

    public String getRequestId() {
        return exchange.getAttributeOrDefault(ServerWebExchange.LOG_ID_ATTRIBUTE, "");
    }
}