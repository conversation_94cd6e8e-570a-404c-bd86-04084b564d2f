package com.medco.HealthConnect.health;


import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.ReactiveHealthIndicator;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Component
public class PayerServiceHealthIndicator implements ReactiveHealthIndicator {

    private final WebClient webClient;

    public PayerServiceHealthIndicator(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder.baseUrl("http://nyalabackend.medcoanalytics.com").build();
    }

    @Override
    public Mono<Health> health() {
        return webClient.get()
                .uri("/actuator/health")
                .retrieve()
                .bodyToMono(String.class)
                .map(s -> Health.up().build())
                .onErrorResume(ex -> Mono.just(Health.down(ex).build()));
    }
}
