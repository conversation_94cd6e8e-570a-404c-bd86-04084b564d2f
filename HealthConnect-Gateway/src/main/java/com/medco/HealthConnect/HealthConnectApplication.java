package com.medco.HealthConnect;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.mongodb.repository.config.EnableReactiveMongoRepositories;

@SpringBootApplication
@EnableReactiveMongoRepositories(basePackages = "com.medco.HealthConnect.repository")
public class HealthConnectApplication {

	public static void main(String[] args) {
		SpringApplication.run(HealthConnectApplication.class, args);
	}

}
