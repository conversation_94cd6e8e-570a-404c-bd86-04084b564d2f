package com.medco.HealthConnect.repository;

import com.medco.HealthConnect.entity.AuditEntry;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

import java.time.Instant;

@Repository
public interface AuditRepository extends ReactiveMongoRepository<AuditEntry, String> {
    Flux<AuditEntry> findByTimestampBetween(Instant from, Instant to);
}
