package com.medco.HealthConnect.service;

import com.medco.HealthConnect.entity.AuditEntry;
import com.medco.HealthConnect.repository.AuditRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class SimpleAuditService {

    private static final Logger logger = LoggerFactory.getLogger(SimpleAuditService.class);
    private final AuditRepository auditRepository;

    public SimpleAuditService(AuditRepository auditRepository) {
        this.auditRepository = auditRepository;
    }

    public Mono<Void> logRequest(ServerWebExchange exchange) {
        long startTime = System.currentTimeMillis();
        exchange.getAttributes().put("startTime", startTime);

        AuditEntry entry = createBaseEntry(exchange);
        entry.setUserId("anonymous"); // For development
        entry.setStatus("REQUEST_RECEIVED");

        Long requestStartTime = exchange.getAttribute("startTime");
        long processingTime = requestStartTime != null ?
                System.currentTimeMillis() - requestStartTime : 0L;
        entry.setProcessingTimeMs(processingTime);

        return auditRepository.save(entry)
                .doOnSuccess(e -> logger.info("Audit log created for {} {} in {}ms",
                        e.getHttpMethod(), e.getPath(), processingTime))
                .then();
    }

    public Mono<Void> logFallback(ServerWebExchange exchange, String reason) {
        AuditEntry entry = createBaseEntry(exchange);
        entry.setStatus("FALLBACK_TRIGGERED");
        entry.setErrorDetails(reason);

        return auditRepository.save(entry)
                .doOnSuccess(e -> logger.warn("Fallback logged: {}", reason))
                .then();
    }

    public Flux<AuditEntry> getAuditLogs(Instant from, Instant to) {
        return auditRepository.findByTimestampBetween(from, to);
    }

    private AuditEntry createBaseEntry(ServerWebExchange exchange) {
        ServerHttpRequest request = exchange.getRequest();
        AuditEntry entry = new AuditEntry();

        entry.setTimestamp(Instant.now());
        entry.setRequestId(exchange.getLogPrefix());
        entry.setHttpMethod(request.getMethod().name());
        entry.setPath(request.getPath().toString());
        entry.setRemoteAddress(request.getRemoteAddress() != null ? 
                request.getRemoteAddress().getAddress().getHostAddress() : "unknown");

        // Convert headers to Map<String, String>
        Map<String, String> headerMap = request.getHeaders().entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> String.join(", ", e.getValue())
                ));
        entry.setHeaders(headerMap);

        // Convert query params to Map<String, String>
        Map<String, String> queryMap = request.getQueryParams().entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> String.join(", ", e.getValue())
                ));
        entry.setQueryParams(queryMap);

        return entry;
    }
}
