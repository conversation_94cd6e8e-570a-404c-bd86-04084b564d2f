package com.medco.HealthConnect.service;

import com.medco.HealthConnect.entity.AuditEntry;
import com.medco.HealthConnect.repository.AuditRepository;
import io.micrometer.tracing.Tracer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
// OAuth2 imports removed for development
import org.springframework.stereotype.Service;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import org.springframework.security.core.Authentication;
import java.util.Collection;
import java.util.Collections;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class AuditService {

    private static final Logger logger = LoggerFactory.getLogger(AuditService.class);
    private final AuditRepository auditRepository;
    private final Tracer tracer;

    public AuditService(AuditRepository auditRepository, Tracer tracer) {
        this.auditRepository = auditRepository;
        this.tracer = tracer;
    }

    public Mono<Void> logRequest(ServerWebExchange exchange) {
        // Record start time at the beginning of processing
        long startTime = System.currentTimeMillis();
        exchange.getAttributes().put("startTime", startTime);

        return exchange.getPrincipal()
                .cast(Authentication.class)
                .defaultIfEmpty(new AnonymousAuthenticationToken())
                .flatMap(principal -> {
                    AuditEntry entry = createBaseEntry(exchange);

                    // Set authentication info
                    entry.setUserId(principal.getName());
                    entry.setUserRoles(principal.getAuthorities().toString());

                    // Set tracing info
                    if (tracer.currentSpan() != null) {
                        entry.setTraceId(tracer.currentSpan().context().traceId());
                        entry.setSpanId(tracer.currentSpan().context().spanId());
                    }

                    // Calculate processing time safely
                    Long requestStartTime = exchange.getAttribute("startTime");
                    long processingTime = requestStartTime != null ?
                            System.currentTimeMillis() - requestStartTime : 0L;
                    entry.setProcessingTimeMs(processingTime);

                    return auditRepository.save(entry)
                            .doOnSuccess(e -> logger.info("Audit log created for {} {} in {}ms",
                                    e.getHttpMethod(), e.getPath(), processingTime))
                            .then();
                });
    }

    public Mono<Void> logFallback(ServerWebExchange exchange, String reason) {
        AuditEntry entry = createBaseEntry(exchange);
        entry.setStatus("FALLBACK_TRIGGERED");
        entry.setErrorDetails(reason);
        entry.setTraceId(tracer.currentSpan() != null ?
                tracer.currentSpan().context().traceId() : "none");

        return auditRepository.save(entry)
                .doOnSuccess(e -> logger.warn("Fallback logged: {}", reason))
                .then();
    }

    public Mono<Void> logValidationFailure(ServerWebExchange exchange, List<String> errors) {
        AuditEntry entry = createBaseEntry(exchange);
        entry.setStatus("VALIDATION_FAILED");
        entry.setErrorDetails(errors.toString());
        entry.setTraceId(tracer.currentSpan() != null ?
                tracer.currentSpan().context().traceId() : "none");

        return auditRepository.save(entry)
                .doOnSuccess(e -> logger.warn("Validation failed: {}", errors))
                .then();
    }

    public Flux<AuditEntry> getAuditLogs(Instant from, Instant to) {
        return auditRepository.findByTimestampBetween(from, to);
    }

    private AuditEntry createBaseEntry(ServerWebExchange exchange) {
        ServerHttpRequest request = exchange.getRequest();
        AuditEntry entry = new AuditEntry();

        entry.setTimestamp(Instant.now());
        entry.setRequestId(exchange.getAttributeOrDefault(ServerWebExchange.LOG_ID_ATTRIBUTE, ""));
        entry.setHttpMethod(request.getMethod().name()); // Changed from getMethodValue()

        // Convert headers to Map<String, String>
        Map<String, String> headerMap = request.getHeaders().entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> String.join(", ", e.getValue())
                ));

        // Convert query params to Map<String, String>
        Map<String, String> queryParamMap = request.getQueryParams().entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> String.join(", ", e.getValue())
                ));

        entry.setPath(request.getPath().toString());
        entry.setRemoteAddress(request.getRemoteAddress() != null ?
                request.getRemoteAddress().getAddress().getHostAddress() : "");
        entry.setHeaders(headerMap);
        entry.setQueryParams(queryParamMap);

        return entry;
    }

    private static class AnonymousAuthenticationToken implements Authentication {
        private static final Collection<GrantedAuthority> ANONYMOUS_AUTHORITIES =
                Collections.singletonList(new SimpleGrantedAuthority("ROLE_ANONYMOUS"));

        @Override
        public Collection<? extends GrantedAuthority> getAuthorities() {
            return ANONYMOUS_AUTHORITIES;
        }

        @Override
        public Object getCredentials() {
            return null;
        }

        @Override
        public Object getDetails() {
            return null;
        }

        @Override
        public Object getPrincipal() {
            return "anonymous";
        }

        @Override
        public boolean isAuthenticated() {
            return false;
        }

        @Override
        public void setAuthenticated(boolean isAuthenticated) {
            throw new UnsupportedOperationException();
        }

        @Override
        public String getName() {
            return "anonymous";
        }
    }
}