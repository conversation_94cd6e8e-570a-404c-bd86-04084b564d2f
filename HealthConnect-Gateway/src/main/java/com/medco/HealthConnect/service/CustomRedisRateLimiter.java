package com.medco.HealthConnect.service;

import io.micrometer.tracing.Tracer;
import org.springframework.cloud.gateway.filter.ratelimit.RateLimiter;
import org.springframework.cloud.gateway.filter.ratelimit.RedisRateLimiter;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class CustomRedisRateLimiter {

    private final Tracer tracer;
    private final Map<String, Config> routeConfigs = new ConcurrentHashMap<>();

    private static final String REMAINING_HEADER = "X-RateLimit-Remaining";
    private static final String REPLENISH_HEADER = "X-RateLimit-Replenish-Rate";
    private static final String BURST_HEADER = "X-RateLimit-Burst-Capacity";

    public CustomRedisRateLimiter(Tracer tracer) {
        this.tracer = tracer;
    }

    public Mono<RateLimiter.Response> isAllowed(String routeId, String id) {
        Config config = routeConfigs.getOrDefault(routeId, getDefaultConfig());

        // Create a Redis rate limiter with the specific config
        RedisRateLimiter redisRateLimiter = new RedisRateLimiter(
                config.getReplenishRate(),
                config.getBurstCapacity()
        );

        // Add tracing context to rate limit key
        String tracedId = tracer.currentSpan() != null
                ? id + "|trace:" + tracer.currentSpan().context().traceId()
                : id;

        return redisRateLimiter.isAllowed(routeId, tracedId)
                .map(response -> {
                    // Extract tokens remaining from response headers if available
                    String tokensRemaining = "0";
                    if (response.getHeaders() != null && response.getHeaders().containsKey("X-RateLimit-Remaining")) {
                        tokensRemaining = response.getHeaders().get("X-RateLimit-Remaining");
                    }

                    // Create new response with additional headers
                    Map<String, String> headers = Map.of(
                            REMAINING_HEADER, tokensRemaining,
                            REPLENISH_HEADER, String.valueOf(config.getReplenishRate()),
                            BURST_HEADER, String.valueOf(config.getBurstCapacity())
                    );

                    return new RateLimiter.Response(
                            response.isAllowed(),
                            headers
                    );
                });
    }

    public void configureRoute(String routeId, Config config) {
        routeConfigs.put(routeId, config);
    }

    private Config getDefaultConfig() {
        Config config = new Config();
        config.setReplenishRate(10);
        config.setBurstCapacity(20);
        return config;
    }

    public static class Config {
        private int replenishRate;
        private int burstCapacity;

        public int getReplenishRate() {
            return replenishRate;
        }

        public void setReplenishRate(int replenishRate) {
            this.replenishRate = replenishRate;
        }

        public int getBurstCapacity() {
            return burstCapacity;
        }

        public void setBurstCapacity(int burstCapacity) {
            this.burstCapacity = burstCapacity;
        }
    }
}