package com.medco.HealthConnect.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.medco.HealthConnect.entity.ClaimRequest;
import com.medco.HealthConnect.entity.Procedure;
import com.medco.HealthConnect.exceptions.ClaimValidationException;
import com.medco.HealthConnect.service.AuditService;
import io.micrometer.tracing.Tracer;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.http.HttpMethod;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Component
public class ClaimValidationFilter extends AbstractGatewayFilterFactory<ClaimValidationFilter.Config> {

    private final ObjectMapper objectMapper;
    private final AuditService auditService;
    private final Tracer tracer;

    private final DataBufferFactory bufferFactory = new DefaultDataBufferFactory();

    public ClaimValidationFilter(ObjectMapper objectMapper,
                                 AuditService auditService,
                                 Tracer tracer) {
        super(Config.class);
        this.objectMapper = objectMapper;
        this.auditService = auditService;
        this.tracer = tracer;
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            ServerHttpRequest request = exchange.getRequest();

            // Skip validation for GET requests
            if (request.getMethod() == HttpMethod.GET) {
                return chain.filter(exchange);
            }

            // Store the original request body as a attribute
            return DataBufferUtils.join(request.getBody())
                    .flatMap(dataBuffer -> {
                        try {
                            byte[] bytes = new byte[dataBuffer.readableByteCount()];
                            dataBuffer.read(bytes);
                            DataBufferUtils.release(dataBuffer);

                            String requestBody = new String(bytes, StandardCharsets.UTF_8);
                            ClaimRequest claim = objectMapper.readValue(requestBody, ClaimRequest.class);

                            // Validate required fields
                            List<String> errors = validateClaim(claim);

                            if (!errors.isEmpty()) {
                                return auditService.logValidationFailure(exchange, errors)
                                        .then(Mono.error(new ClaimValidationException(
                                                "Invalid claim data",
                                                errors,
                                                exchange
                                        )));
                            }

                            // Add tracing context to claim
                            if (tracer.currentSpan() != null) {
                                claim.setTraceId(tracer.currentSpan().context().traceId());
                                claim.setSpanId(tracer.currentSpan().context().spanId());
                            }

                            // Add validated claim to attributes for downstream services
                            exchange.getAttributes().put("validatedClaim", claim);

                            // Create a new exchange with the modified body
                            return chain.filter(exchange.mutate().request(
                                    new ServerHttpRequestDecorator(request) {
                                        @Override
                                        public Flux<DataBuffer> getBody() {
                                            byte[] modifiedBytes;
                                            try {
                                                modifiedBytes = objectMapper.writeValueAsBytes(claim);
                                            } catch (Exception e) {
                                                return Flux.error(e);
                                            }
                                            return Flux.just(bufferFactory.wrap(modifiedBytes));
                                        }
                                    }
                            ).build());
                        } catch (Exception e) {
                            return auditService.logValidationFailure(exchange, List.of(e.getMessage()))
                                    .then(Mono.error(new ClaimValidationException(
                                            "Error processing claim data: " + e.getMessage(),
                                            List.of(e.getMessage()),
                                            exchange
                                    )));
                        }
                    });
        };
    }

    private List<String> validateClaim(ClaimRequest claim) {
        List<String> errors = new ArrayList<>();

        if (claim.getPatientId() == null || claim.getPatientId().isEmpty()) {
            errors.add("Patient ID is required");
        } else if (!claim.getPatientId().matches("^[A-Z]{2}\\d{6}$")) {
            errors.add("Patient ID must be in format AA123456");
        }

        if (claim.getProviderId() == null || claim.getProviderId().isEmpty()) {
            errors.add("Provider ID is required");
        }

        if (claim.getServiceDate() == null) {
            errors.add("Service date is required");
        }

        if (claim.getProcedures() == null || claim.getProcedures().isEmpty()) {
            errors.add("At least one procedure is required");
        } else {
            for (Procedure procedure : claim.getProcedures()) {
                if (procedure.getCode() == null || procedure.getCode().isEmpty()) {
                    errors.add("Procedure code is required for all procedures");
                } else if (!procedure.getCode().matches("^[A-Z0-9]{5}$")) {
                    errors.add("Procedure code must be 5 alphanumeric characters");
                }

                if (procedure.getDescription() == null || procedure.getDescription().isEmpty()) {
                    errors.add("Procedure description is required for all procedures");
                }
            }
        }

        return errors;
    }

    public static class Config {
        // Add configuration properties here if needed
        private boolean strictValidation = true;

        public boolean isStrictValidation() {
            return strictValidation;
        }

        public void setStrictValidation(boolean strictValidation) {
            this.strictValidation = strictValidation;
        }
    }
}